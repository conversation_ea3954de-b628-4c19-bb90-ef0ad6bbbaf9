/**
 * Excel文件解析脚本 - 解析行业股票信息表
 * 使用xlsx库解析industry_to_stock.xlsx文件并转换为数据库模型格式
 *
 * <AUTHOR>
 * @created 2025-01-27 16:47:31
 * @updated 2025-01-27 16:47:31
 * @description 解析行业股票Excel文件，输出完整数据并保存为JSON文件，字段映射遵循TonghuashunStockIndustryInfo模型规范
 */

const XLSX = require('xlsx');
const path = require('path');
const fs = require('fs');

/**
 * 字段映射配置 - Excel字段名到数据库字段名的映射
 * 遵循TonghuashunStockIndustryInfo模型规范
 * @type {Object}
 */
const FIELD_MAPPING = {
    '证券代码': 'securityCode',
    '证券名称': 'securityName',
    '公司中文名称': 'companyFullName',
    '所属新证监会行业\n[行业级别]  门类行业\n[截止日期]  最新': 'industryCategory',
    '所属新证监会行业\n[行业级别]  大类行业\n[截止日期]  最新': 'industryBigCategory',
    '所属新证监会行业\n[行业级别]  全部明细\n[截止日期]  最新': 'industryDetail'
};

/**
 * 数据清洗和转换函数
 * @param {any} value - 原始值
 * @param {string} fieldName - 字段名
 * @returns {any} 清洗后的值
 */
function cleanValue(value, fieldName) {
    // 处理空值和"--"
    if (value === null || value === undefined || value === '--' || value === '') {
        return null;
    }

    // 字符串字段处理 - 去除首尾空格
    if (typeof value === 'string') {
        const trimmed = value.trim();
        return trimmed === '' ? null : trimmed;
    }

    // 其他类型转换为字符串
    return String(value).trim();
}

/**
 * 转换单条记录为数据库模型格式
 * @param {Object} record - Excel原始记录
 * @returns {Object} 转换后的记录
 */
function transformRecord(record) {
    const transformed = {};
    
    // 遍历字段映射，转换数据
    for (const [excelField, dbField] of Object.entries(FIELD_MAPPING)) {
        const rawValue = record[excelField];
        transformed[dbField] = cleanValue(rawValue, dbField);
    }
    
    // 添加表更新时间
    transformed.updateTime = new Date().toISOString();
    
    return transformed;
}

/**
 * 确保目录存在
 * @param {string} dirPath - 目录路径
 */
function ensureDirectoryExists(dirPath) {
    if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
        console.log(`创建目录: ${dirPath}`);
    }
}

/**
 * 解析Excel文件并输出完整数据为JSON格式
 * @param {string} filePath - Excel文件路径
 */
function parseExcelFileAll(filePath) {
    try {
        // 检查文件是否存在
        if (!fs.existsSync(filePath)) {
            console.error(`文件不存在: ${filePath}`);
            return;
        }

        console.log(`开始解析文件: ${filePath}`);
        console.log(`解析时间: ${new Date().toLocaleString()}`);
        console.log('='.repeat(50));

        // 读取Excel文件
        const workbook = XLSX.readFile(filePath);
        
        // 获取第一个工作表名称
        const sheetName = workbook.SheetNames[0];
        console.log(`工作表名称: ${sheetName}`);
        
        // 获取工作表数据
        const worksheet = workbook.Sheets[sheetName];
        
        // 将工作表转换为JSON格式
        const jsonData = XLSX.utils.sheet_to_json(worksheet);
        
        console.log(`总记录数: ${jsonData.length}`);
        console.log('='.repeat(50));
        
        // 显示原始数据的前10条记录
        console.log('\n原始数据前10条记录:');
        console.log('='.repeat(50));
        console.log(JSON.stringify(jsonData.slice(0, 10), null, 2));
        
        // 转换所有数据
        console.log('\n开始转换所有数据...');

        const transformedData = jsonData.map((record, index) => {
            try {
                return transformRecord(record);
            } catch (error) {
                console.error(`转换第${index + 1}条记录时出错:`, error.message);
                return null;
            }
        }).filter(record => record !== null);
        
        console.log(`成功转换记录数: ${transformedData.length}`);

        // 显示转换后的前10条数据作为预览
        console.log('\n转换后的前10条数据预览:');
        console.log('='.repeat(50));
        console.log(JSON.stringify(transformedData.slice(0, 10), null, 2));
        
        // 确保data目录存在
        const dataDir = path.join(process.cwd(), 'data');
        ensureDirectoryExists(dataDir);
        
        // 保存转换后的数据
        const outputPath = path.join(dataDir, 'industry_to_stock.json');
        fs.writeFileSync(outputPath, JSON.stringify(transformedData, null, 2), 'utf8');
        
        console.log(`\n数据已保存到: ${outputPath}`);
        console.log(`文件大小: ${(fs.statSync(outputPath).size / 1024 / 1024).toFixed(2)} MB`);
        console.log('\n解析完成！');
        
    } catch (error) {
        console.error('解析Excel文件时发生错误:', error.message);
        console.error('错误详情:', error);
    }
}

// 主执行函数
function main() {
    const filePath = path.join(process.cwd(), 'industry_to_stock.xlsx');
    parseExcelFileAll(filePath);
}

// 执行脚本
if (require.main === module) {
    main();
}

module.exports = { parseExcelFileAll, transformRecord, cleanValue };
