/**
 * 基金行业配置数据验证脚本
 * 验证导入到数据库的数据完整性和正确性
 * 
 * <AUTHOR>
 * @created 2025-08-04 11:05:00
 * @updated 2025-08-04 11:05:00
 * @description 验证tonghuashun_fund_industry_info表中的数据
 */

const { Pool } = require('pg');

/**
 * PostgreSQL数据库连接配置
 */
const DB_CONFIG = {
    user: 'starlink_dev',
    host: '***************',
    database: 'starlink_tonghuasun_data',
    password: 'dev_2706',
    port: 5432,
    max: 20,
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: 2000,
};

const pool = new Pool(DB_CONFIG);
const TABLE_NAME = 'tonghuashun_fund_industry_info';

/**
 * 验证数据库连接
 */
async function checkConnection() {
    try {
        const client = await pool.connect();
        await client.query('SELECT 1');
        client.release();
        console.log('✅ 数据库连接成功');
        return true;
    } catch (error) {
        console.error('❌ 数据库连接失败:', error.message);
        return false;
    }
}

/**
 * 获取基本统计信息
 */
async function getBasicStats() {
    const client = await pool.connect();
    try {
        console.log('\n📊 基本统计信息:');
        console.log('='.repeat(50));

        // 总记录数
        const totalResult = await client.query(`SELECT COUNT(*) as count FROM ${TABLE_NAME}`);
        console.log(`总记录数: ${totalResult.rows[0].count}`);

        // 基金数量
        const fundResult = await client.query(`SELECT COUNT(DISTINCT fund_code) as count FROM ${TABLE_NAME}`);
        console.log(`基金数量: ${fundResult.rows[0].count}`);

        // 行业数量
        const industryResult = await client.query(`SELECT COUNT(DISTINCT industry_code) as count FROM ${TABLE_NAME}`);
        console.log(`行业代码数量: ${industryResult.rows[0].count}`);

        // 行业名称数量
        const industryNameResult = await client.query(`SELECT COUNT(DISTINCT industry_name) as count FROM ${TABLE_NAME}`);
        console.log(`行业名称数量: ${industryNameResult.rows[0].count}`);

        // 管理公司数量
        const companyResult = await client.query(`SELECT COUNT(DISTINCT management_company) as count FROM ${TABLE_NAME}`);
        console.log(`管理公司数量: ${companyResult.rows[0].count}`);

    } catch (error) {
        console.error('❌ 获取统计信息失败:', error.message);
    } finally {
        client.release();
    }
}

/**
 * 检查数据完整性
 */
async function checkDataIntegrity() {
    const client = await pool.connect();
    try {
        console.log('\n🔍 数据完整性检查:');
        console.log('='.repeat(50));

        // 检查必填字段是否有空值
        const nullCheckFields = ['fund_code', 'industry_code', 'fund_name', 'industry_name'];
        for (const field of nullCheckFields) {
            const result = await client.query(`SELECT COUNT(*) as count FROM ${TABLE_NAME} WHERE ${field} IS NULL OR ${field} = ''`);
            const nullCount = result.rows[0].count;
            if (nullCount > 0) {
                console.log(`❌ ${field} 有 ${nullCount} 条空值记录`);
            } else {
                console.log(`✅ ${field} 无空值`);
            }
        }

        // 检查主键重复
        const duplicateResult = await client.query(`
            SELECT fund_code, industry_code, COUNT(*) as count 
            FROM ${TABLE_NAME} 
            GROUP BY fund_code, industry_code 
            HAVING COUNT(*) > 1
        `);
        if (duplicateResult.rows.length > 0) {
            console.log(`❌ 发现 ${duplicateResult.rows.length} 组重复的主键`);
        } else {
            console.log('✅ 主键无重复');
        }

    } catch (error) {
        console.error('❌ 数据完整性检查失败:', error.message);
    } finally {
        client.release();
    }
}

/**
 * 显示样本数据
 */
async function showSampleData() {
    const client = await pool.connect();
    try {
        console.log('\n📋 样本数据 (前5条):');
        console.log('='.repeat(50));

        const result = await client.query(`
            SELECT 
                fund_code,
                fund_name,
                industry_code,
                industry_name,
                market_value,
                net_value_ratio,
                investment_type,
                management_company
            FROM ${TABLE_NAME} 
            ORDER BY fund_code, industry_code 
            LIMIT 5
        `);

        result.rows.forEach((row, index) => {
            console.log(`\n记录 ${index + 1}:`);
            console.log(`  基金代码: ${row.fund_code}`);
            console.log(`  基金名称: ${row.fund_name}`);
            console.log(`  行业代码: ${row.industry_code}`);
            console.log(`  行业名称: ${row.industry_name}`);
            console.log(`  市值(万元): ${row.market_value}`);
            console.log(`  占净值比(%): ${row.net_value_ratio}`);
            console.log(`  投资类型: ${row.investment_type}`);
            console.log(`  管理公司: ${row.management_company}`);
        });

    } catch (error) {
        console.error('❌ 获取样本数据失败:', error.message);
    } finally {
        client.release();
    }
}

/**
 * 显示行业分布
 */
async function showIndustryDistribution() {
    const client = await pool.connect();
    try {
        console.log('\n🏭 行业分布 (前10个):');
        console.log('='.repeat(50));

        const result = await client.query(`
            SELECT 
                industry_code,
                industry_name,
                COUNT(*) as fund_count,
                ROUND(AVG(market_value), 2) as avg_market_value
            FROM ${TABLE_NAME} 
            WHERE market_value IS NOT NULL
            GROUP BY industry_code, industry_name 
            ORDER BY fund_count DESC 
            LIMIT 10
        `);

        result.rows.forEach((row, index) => {
            console.log(`${index + 1}. ${row.industry_code} - ${row.industry_name}`);
            console.log(`   基金数量: ${row.fund_count}`);
            console.log(`   平均市值: ${row.avg_market_value} 万元`);
        });

    } catch (error) {
        console.error('❌ 获取行业分布失败:', error.message);
    } finally {
        client.release();
    }
}

/**
 * 显示管理公司分布
 */
async function showCompanyDistribution() {
    const client = await pool.connect();
    try {
        console.log('\n🏢 管理公司分布 (前10个):');
        console.log('='.repeat(50));

        const result = await client.query(`
            SELECT 
                management_company,
                COUNT(DISTINCT fund_code) as fund_count,
                COUNT(*) as record_count
            FROM ${TABLE_NAME} 
            GROUP BY management_company 
            ORDER BY fund_count DESC 
            LIMIT 10
        `);

        result.rows.forEach((row, index) => {
            console.log(`${index + 1}. ${row.management_company}`);
            console.log(`   基金数量: ${row.fund_count}`);
            console.log(`   记录数量: ${row.record_count}`);
        });

    } catch (error) {
        console.error('❌ 获取管理公司分布失败:', error.message);
    } finally {
        client.release();
    }
}

/**
 * 主验证函数
 */
async function main() {
    try {
        console.log('🚀 开始验证基金行业配置数据');
        console.log(`验证时间: ${new Date().toLocaleString()}`);
        console.log('='.repeat(60));

        // 检查数据库连接
        const isConnected = await checkConnection();
        if (!isConnected) {
            throw new Error('数据库连接失败');
        }

        // 执行各项验证
        await getBasicStats();
        await checkDataIntegrity();
        await showSampleData();
        await showIndustryDistribution();
        await showCompanyDistribution();

        console.log('\n='.repeat(60));
        console.log('✅ 数据验证完成');
        console.log(`完成时间: ${new Date().toLocaleString()}`);

    } catch (error) {
        console.error('❌ 验证过程发生错误:', error.message);
        process.exit(1);
    } finally {
        await pool.end();
        console.log('🔒 数据库连接池已关闭');
    }
}

// 执行脚本
if (require.main === module) {
    main();
}

module.exports = {
    checkConnection,
    getBasicStats,
    checkDataIntegrity,
    showSampleData,
    showIndustryDistribution,
    showCompanyDistribution
};
