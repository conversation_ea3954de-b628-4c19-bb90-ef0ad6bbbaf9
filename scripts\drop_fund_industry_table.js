/**
 * 删除基金行业配置表脚本
 * 用于重新创建表结构
 * 
 * <AUTHOR>
 * @created 2025-08-04 11:05:00
 * @updated 2025-08-04 11:05:00
 * @description 删除tonghuashun_fund_industry_info表以便重新创建
 */

const { Pool } = require('pg');

/**
 * PostgreSQL数据库连接配置
 */
const DB_CONFIG = {
    user: 'starlink_dev',
    host: '***************',
    database: 'starlink_tonghuasun_data',
    password: 'dev_2706',
    port: 5432,
    max: 20,
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: 2000,
};

const pool = new Pool(DB_CONFIG);
const TABLE_NAME = 'tonghuashun_fund_industry_info';

async function dropTable() {
    const client = await pool.connect();
    try {
        await client.query(`DROP TABLE IF EXISTS ${TABLE_NAME} CASCADE`);
        console.log(`✅ 表 ${TABLE_NAME} 已删除`);
    } catch (error) {
        console.error('❌ 删除表失败:', error.message);
        throw error;
    } finally {
        client.release();
        await pool.end();
    }
}

async function main() {
    try {
        console.log(`正在删除表 ${TABLE_NAME}...`);
        await dropTable();
        console.log('删除完成！');
    } catch (error) {
        console.error('删除失败:', error.message);
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}
