/**
 * 基金行业配置数据导入脚本
 * 从JSON文件读取基金行业配置数据并导入到PostgreSQL数据库
 * 支持批量插入和数据清理功能
 * 
 * <AUTHOR>
 * @created 2025-08-04 11:00:00
 * @updated 2025-08-04 11:00:00
 * @description 将解析后的基金行业配置JSON数据批量导入到tonghuashun_fund_industry_info表中
 */

const fs = require('fs');
const path = require('path');
const { Pool } = require('pg');

/**
 * PostgreSQL数据库连接配置
 * 使用与import_fund_basic_info.js相同的配置
 */
const DB_CONFIG = {
    user: 'starlink_dev',           // 数据库用户名
    host: '***************',        // 数据库主机地址
    database: 'starlink_tonghuasun_data',    // 数据库名称
    password: 'dev_2706',           // 数据库密码
    port: 5432,                     // 数据库端口
    max: 20,                        // 连接池最大连接数
    idleTimeoutMillis: 30000,       // 空闲连接超时时间
    connectionTimeoutMillis: 2000,  // 连接超时时间
};

/**
 * 数据库连接池
 */
const pool = new Pool(DB_CONFIG);

/**
 * 批量插入的批次大小
 */
const BATCH_SIZE = 1000;

/**
 * 数据库表名
 */
const TABLE_NAME = 'tonghuashun_fund_industry_info';

/**
 * 数据库字段映射
 * 将JSON字段映射到数据库字段
 */
const DB_FIELD_MAPPING = {
    fund_code: 'fundCode',
    industry_code: 'industryCode',
    fund_name: 'fundName',
    industry_name: 'industryName',
    market_value: 'marketValue',
    net_value_ratio: 'netValueRatio',
    stock_invest_ratio: 'stockInvestRatio',
    standard_allocation_ratio: 'standardAllocationRatio',
    relative_allocation_ratio: 'relativeAllocationRatio',
    market_value_growth_rate: 'marketValueGrowthRate',
    investment_type: 'investmentType',
    management_company: 'managementCompany',
    update_time: 'updateTime'
};

/**
 * 数据类型转换函数
 * @param {any} value - 原始值
 * @param {string} type - 目标类型
 * @returns {any} 转换后的值
 */
function convertValue(value, type) {
    if (value === null || value === undefined || value === '') {
        return null;
    }

    switch (type) {
        case 'string':
            return String(value).trim();
        case 'decimal':
            const num = parseFloat(value);
            return isNaN(num) ? null : num;
        case 'timestamp':
            if (value instanceof Date) {
                return value;
            }
            const date = new Date(value);
            return isNaN(date.getTime()) ? null : date;
        default:
            return value;
    }
}

/**
 * 将JSON记录转换为数据库记录格式
 * @param {Object} jsonRecord - JSON记录
 * @returns {Object} 数据库记录
 */
function transformToDbRecord(jsonRecord) {
    const dbRecord = {};
    
    // 字段类型定义
    const fieldTypes = {
        fund_code: 'string',
        industry_code: 'string',
        fund_name: 'string',
        industry_name: 'string',
        market_value: 'decimal',
        net_value_ratio: 'decimal',
        stock_invest_ratio: 'decimal',
        standard_allocation_ratio: 'decimal',
        relative_allocation_ratio: 'decimal',
        market_value_growth_rate: 'decimal',
        investment_type: 'string',
        management_company: 'string',
        update_time: 'timestamp'
    };

    // 转换字段
    for (const [dbField, jsonField] of Object.entries(DB_FIELD_MAPPING)) {
        const fieldType = fieldTypes[dbField];
        dbRecord[dbField] = convertValue(jsonRecord[jsonField], fieldType);
    }

    return dbRecord;
}

/**
 * 创建表（如果不存在）
 * @returns {Promise<void>}
 */
async function createTableIfNotExists() {
    const createTableSQL = `
        CREATE TABLE IF NOT EXISTS ${TABLE_NAME} (
            fund_code VARCHAR(50) NOT NULL,
            industry_code VARCHAR(50) NOT NULL,
            fund_name VARCHAR(200) NOT NULL,
            industry_name VARCHAR(200) NOT NULL,
            market_value DECIMAL(15,2),
            net_value_ratio DECIMAL(12,4),
            stock_invest_ratio DECIMAL(12,4),
            standard_allocation_ratio DECIMAL(12,4),
            relative_allocation_ratio DECIMAL(12,4),
            market_value_growth_rate DECIMAL(12,4),
            investment_type VARCHAR(100),
            management_company VARCHAR(200),
            update_time TIMESTAMP NOT NULL,

            PRIMARY KEY (fund_code, industry_code)
        );

        -- 创建索引
        CREATE INDEX IF NOT EXISTS idx_fund_code ON ${TABLE_NAME} (fund_code);
        CREATE INDEX IF NOT EXISTS idx_industry_code ON ${TABLE_NAME} (industry_code);
        CREATE INDEX IF NOT EXISTS idx_industry_name ON ${TABLE_NAME} (industry_name);
        CREATE INDEX IF NOT EXISTS idx_management_company ON ${TABLE_NAME} (management_company);
        CREATE INDEX IF NOT EXISTS idx_update_time ON ${TABLE_NAME} (update_time);
    `;

    const client = await pool.connect();
    try {
        await client.query(createTableSQL);
        console.log('✅ 表结构检查/创建完成');
    } catch (error) {
        console.error('❌ 创建表失败:', error.message);
        throw error;
    } finally {
        client.release();
    }
}

/**
 * 生成批量插入SQL
 * @param {Array} records - 记录数组
 * @returns {Object} SQL语句和参数
 */
function generateBatchInsertSQL(records) {
    if (records.length === 0) {
        return { sql: null, values: [] };
    }

    const fields = Object.keys(DB_FIELD_MAPPING);
    const fieldList = fields.join(', ');
    
    const values = [];
    const valuePlaceholders = [];
    
    records.forEach((record, recordIndex) => {
        const recordValues = [];
        const recordPlaceholders = [];
        
        fields.forEach((field, fieldIndex) => {
            const paramIndex = recordIndex * fields.length + fieldIndex + 1;
            recordPlaceholders.push(`$${paramIndex}`);
            recordValues.push(record[field]);
        });
        
        valuePlaceholders.push(`(${recordPlaceholders.join(', ')})`);
        values.push(...recordValues);
    });

    const sql = `
        INSERT INTO ${TABLE_NAME} (${fieldList})
        VALUES ${valuePlaceholders.join(', ')}
        ON CONFLICT (fund_code, industry_code) DO UPDATE SET
            ${fields.filter(f => f !== 'fund_code' && f !== 'industry_code').map(f => `${f} = EXCLUDED.${f}`).join(', ')}
    `;

    return { sql, values };
}

/**
 * 批量插入数据到数据库
 * @param {Array} records - 记录数组
 * @returns {Promise<number>} 插入的记录数
 */
async function batchInsertRecords(records) {
    if (records.length === 0) {
        return 0;
    }

    const client = await pool.connect();
    let insertedCount = 0;

    try {
        await client.query('BEGIN');

        // 分批处理
        for (let i = 0; i < records.length; i += BATCH_SIZE) {
            const batch = records.slice(i, i + BATCH_SIZE);
            const { sql, values } = generateBatchInsertSQL(batch);
            
            if (sql) {
                await client.query(sql, values);
                insertedCount += batch.length;
                console.log(`已处理 ${insertedCount}/${records.length} 条记录`);
            }
        }

        await client.query('COMMIT');
        console.log('数据导入事务提交成功');
        
    } catch (error) {
        await client.query('ROLLBACK');
        console.error('数据导入失败，事务已回滚:', error.message);
        throw error;
    } finally {
        client.release();
    }

    return insertedCount;
}

/**
 * 清空表数据
 * @returns {Promise<void>}
 */
async function clearTable() {
    const client = await pool.connect();
    try {
        await client.query(`TRUNCATE TABLE ${TABLE_NAME} RESTART IDENTITY CASCADE`);
        console.log(`表 ${TABLE_NAME} 已清空`);
    } catch (error) {
        console.error('清空表失败:', error.message);
        throw error;
    } finally {
        client.release();
    }
}

/**
 * 检查数据库连接
 * @returns {Promise<boolean>} 连接是否成功
 */
async function checkDatabaseConnection() {
    try {
        const client = await pool.connect();
        await client.query('SELECT 1');
        client.release();
        console.log('数据库连接成功');
        return true;
    } catch (error) {
        console.error('数据库连接失败:', error.message);
        console.error('请检查数据库配置和连接参数');
        return false;
    }
}

/**
 * 获取表记录统计
 * @returns {Promise<number>} 记录总数
 */
async function getTableCount() {
    const client = await pool.connect();
    try {
        const result = await client.query(`SELECT COUNT(*) as count FROM ${TABLE_NAME}`);
        return parseInt(result.rows[0].count, 10);
    } catch (error) {
        console.error('获取表记录数失败:', error.message);
        return 0;
    } finally {
        client.release();
    }
}

/**
 * 主导入函数
 * @param {string} jsonFilePath - JSON文件路径
 * @param {boolean} clearFirst - 是否先清空表
 * @returns {Promise<void>}
 */
async function importFundIndustryInfo(jsonFilePath, clearFirst = false) {
    console.log('='.repeat(60));
    console.log('基金行业配置数据导入开始');
    console.log(`导入时间: ${new Date().toLocaleString()}`);
    console.log('='.repeat(60));

    try {
        // 检查数据库连接
        const isConnected = await checkDatabaseConnection();
        if (!isConnected) {
            throw new Error('数据库连接失败，请检查配置');
        }

        // 创建表结构
        await createTableIfNotExists();

        // 检查JSON文件是否存在
        if (!fs.existsSync(jsonFilePath)) {
            throw new Error(`JSON文件不存在: ${jsonFilePath}`);
        }

        // 读取JSON数据
        console.log(`读取JSON文件: ${jsonFilePath}`);
        const jsonData = JSON.parse(fs.readFileSync(jsonFilePath, 'utf8'));
        console.log(`JSON文件记录数: ${jsonData.length}`);

        // 获取导入前的记录数
        const beforeCount = await getTableCount();
        console.log(`导入前表记录数: ${beforeCount}`);

        // 是否清空表
        if (clearFirst) {
            console.log('清空表数据...');
            await clearTable();
        }

        // 转换数据格式
        console.log('开始数据格式转换...');
        const dbRecords = jsonData.map((record, index) => {
            try {
                return transformToDbRecord(record);
            } catch (error) {
                console.error(`转换第${index + 1}条记录失败:`, error.message);
                return null;
            }
        }).filter(record => record !== null);

        console.log(`成功转换记录数: ${dbRecords.length}`);

        // 批量导入数据
        console.log('开始批量导入数据...');
        const insertedCount = await batchInsertRecords(dbRecords);

        // 获取导入后的记录数
        const afterCount = await getTableCount();
        console.log(`导入后表记录数: ${afterCount}`);

        console.log('='.repeat(60));
        console.log('数据导入完成');
        console.log(`成功导入记录数: ${insertedCount}`);
        console.log(`完成时间: ${new Date().toLocaleString()}`);
        console.log('='.repeat(60));

    } catch (error) {
        console.error('导入过程发生错误:', error.message);
        console.error('错误详情:', error);
        throw error;
    } finally {
        // 关闭数据库连接池
        await pool.end();
        console.log('数据库连接池已关闭');
    }
}

/**
 * 主执行函数
 */
async function main() {
    try {
        // 固定使用data目录下的fund_industry_info.json文件
        const jsonFilePath = path.join(process.cwd(), 'data', 'fund_industry_info.json');

        console.log('='.repeat(60));
        console.log('基金行业配置数据导入测试');
        console.log(`当前时间: ${new Date().toLocaleString()}`);
        console.log('='.repeat(60));

        // 检查文件是否存在
        if (!fs.existsSync(jsonFilePath)) {
            console.error(`❌ 数据文件不存在: ${jsonFilePath}`);
            console.log('请确保已运行Excel解析脚本生成JSON文件');
            process.exit(1);
        }

        // 显示数据库配置信息（隐藏密码）
        console.log('数据库配置:');
        console.log(`  主机: ${DB_CONFIG.host}:${DB_CONFIG.port}`);
        console.log(`  数据库: ${DB_CONFIG.database}`);
        console.log(`  用户: ${DB_CONFIG.user}`);
        console.log(`  表名: ${TABLE_NAME}`);
        console.log('');

        // 导入数据（不清空表）
        await importFundIndustryInfo(jsonFilePath, false);

    } catch (error) {
        console.error('❌ 程序执行失败:', error.message);
        console.error('错误详情:', error);
        process.exit(1);
    }
}

// 执行脚本
if (require.main === module) {
    main();
}

module.exports = {
    importFundIndustryInfo,
    transformToDbRecord,
    createTableIfNotExists,
    batchInsertRecords,
    checkDatabaseConnection,
    getTableCount,
    clearTable
};
